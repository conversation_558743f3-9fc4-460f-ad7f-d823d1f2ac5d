"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { PhoneNumberInput, CountrySelect } from "@/components/ui/phone-input";
import { AuthCard } from "./AuthCard";
import { AuthHeader } from "./AuthHeader";
import { isPossiblePhoneNumber, parsePhoneNumber, getCountryCallingCode } from "react-phone-number-input";
import { Value, Country } from "react-phone-number-input";

interface PhoneAuthStepProps {
  onContinue: (phone: string, countryCode: string) => void;
  onSwitchToEmail: () => void;
}

export function PhoneAuthStep({ onContinue, onSwitchToEmail }: PhoneAuthStepProps) {
  const [phoneValue, setPhoneValue] = useState<Value | undefined>();
  const [selectedCountry, setSelectedCountry] = useState<Country>("US");
  const [isValid, setIsValid] = useState(false);

  const handlePhoneChange = (value: Value | undefined) => {
    setPhoneValue(value);
    setIsValid(value ? isPossiblePhoneNumber(value) : false);

    // Update selected country when phone number changes
    if (value) {
      try {
        const parsed = parsePhoneNumber(value);
        if (parsed && parsed.country) {
          setSelectedCountry(parsed.country);
        }
      } catch (error) {
        // If parsing fails, keep the current selected country
      }
    }
  };

  const handleCountryChange = (country: Country) => {
    setSelectedCountry(country);

    // Create a new phone value with the selected country code
    const countryCode = `+${getCountryCallingCode(country)}`;
    setPhoneValue(countryCode as Value);
    setIsValid(false); // Reset validation since we only have country code
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (isValid && phoneValue) {
      onContinue(phoneValue, selectedCountry);
    }
  };

  return (
    <AuthCard>
      <AuthHeader />

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-2">
          <Label className="text-sm font-medium text-auth-text-primary">
            Phone Number
          </Label>
          <div className="flex gap-3">
            <CountrySelect
              value={phoneValue?.country || "US"}
              onChange={(value) => setPhoneValue(value as Value)}
            />
            <PhoneNumberInput
              value={phoneValue}
              onChange={handlePhoneChange}
              placeholder="123 456 789"
              className="h-12 w-full rounded-full overflow-hidden"
            />
          </div>
        </div>

        <Button
          type="submit"
          disabled={!isValid}
          className="w-full h-12 bg-lendbloc-blue hover:bg-lendbloc-blue-dark text-white rounded-full font-medium"
        >
          Continue
        </Button>

        <div className="text-center">
          <Button
            type="button"
            variant="link"
            onClick={onSwitchToEmail}
            className="text-lendbloc-blue hover:text-lendbloc-blue-dark underline"
          >
            Use email address
          </Button>
        </div>
      </form>
    </AuthCard>
  );
}