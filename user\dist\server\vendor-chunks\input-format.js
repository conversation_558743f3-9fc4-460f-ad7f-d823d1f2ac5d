"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/input-format";
exports.ids = ["vendor-chunks/input-format"];
exports.modules = {

/***/ "(ssr)/./node_modules/input-format/modules/closeBraces.js":
/*!**********************************************************!*\
  !*** ./node_modules/input-format/modules/closeBraces.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ closeBraces)\n/* harmony export */ });\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers.js */ \"(ssr)/./node_modules/input-format/modules/helpers.js\");\n\nfunction closeBraces(retained_template, template) {\n  var placeholder = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'x';\n  var empty_placeholder = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : ' ';\n  var cut_before = retained_template.length;\n  var opening_braces = (0,_helpers_js__WEBPACK_IMPORTED_MODULE_0__.count_occurences)('(', retained_template);\n  var closing_braces = (0,_helpers_js__WEBPACK_IMPORTED_MODULE_0__.count_occurences)(')', retained_template);\n  var dangling_braces = opening_braces - closing_braces;\n\n  while (dangling_braces > 0 && cut_before < template.length) {\n    retained_template += template[cut_before].replace(placeholder, empty_placeholder);\n\n    if (template[cut_before] === ')') {\n      dangling_braces--;\n    }\n\n    cut_before++;\n  }\n\n  return retained_template;\n}\n//# sourceMappingURL=closeBraces.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaW5wdXQtZm9ybWF0L21vZHVsZXMvY2xvc2VCcmFjZXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBZ0Q7QUFDakM7QUFDZjtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsNkRBQWdCO0FBQ3ZDLHVCQUF1Qiw2REFBZ0I7QUFDdkM7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcc29sb21cXERvY3VtZW50c1xcMDAxV29ya1Byb2plY3RcXGxlbmRibG9jXFx1c2VyXFxub2RlX21vZHVsZXNcXGlucHV0LWZvcm1hdFxcbW9kdWxlc1xcY2xvc2VCcmFjZXMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY291bnRfb2NjdXJlbmNlcyB9IGZyb20gJy4vaGVscGVycy5qcyc7XG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBjbG9zZUJyYWNlcyhyZXRhaW5lZF90ZW1wbGF0ZSwgdGVtcGxhdGUpIHtcbiAgdmFyIHBsYWNlaG9sZGVyID0gYXJndW1lbnRzLmxlbmd0aCA+IDIgJiYgYXJndW1lbnRzWzJdICE9PSB1bmRlZmluZWQgPyBhcmd1bWVudHNbMl0gOiAneCc7XG4gIHZhciBlbXB0eV9wbGFjZWhvbGRlciA9IGFyZ3VtZW50cy5sZW5ndGggPiAzICYmIGFyZ3VtZW50c1szXSAhPT0gdW5kZWZpbmVkID8gYXJndW1lbnRzWzNdIDogJyAnO1xuICB2YXIgY3V0X2JlZm9yZSA9IHJldGFpbmVkX3RlbXBsYXRlLmxlbmd0aDtcbiAgdmFyIG9wZW5pbmdfYnJhY2VzID0gY291bnRfb2NjdXJlbmNlcygnKCcsIHJldGFpbmVkX3RlbXBsYXRlKTtcbiAgdmFyIGNsb3NpbmdfYnJhY2VzID0gY291bnRfb2NjdXJlbmNlcygnKScsIHJldGFpbmVkX3RlbXBsYXRlKTtcbiAgdmFyIGRhbmdsaW5nX2JyYWNlcyA9IG9wZW5pbmdfYnJhY2VzIC0gY2xvc2luZ19icmFjZXM7XG5cbiAgd2hpbGUgKGRhbmdsaW5nX2JyYWNlcyA+IDAgJiYgY3V0X2JlZm9yZSA8IHRlbXBsYXRlLmxlbmd0aCkge1xuICAgIHJldGFpbmVkX3RlbXBsYXRlICs9IHRlbXBsYXRlW2N1dF9iZWZvcmVdLnJlcGxhY2UocGxhY2Vob2xkZXIsIGVtcHR5X3BsYWNlaG9sZGVyKTtcblxuICAgIGlmICh0ZW1wbGF0ZVtjdXRfYmVmb3JlXSA9PT0gJyknKSB7XG4gICAgICBkYW5nbGluZ19icmFjZXMtLTtcbiAgICB9XG5cbiAgICBjdXRfYmVmb3JlKys7XG4gIH1cblxuICByZXR1cm4gcmV0YWluZWRfdGVtcGxhdGU7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jbG9zZUJyYWNlcy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/closeBraces.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/dom.js":
/*!**************************************************!*\
  !*** ./node_modules/input-format/modules/dom.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Keys: () => (/* binding */ Keys),\n/* harmony export */   getCaretPosition: () => (/* binding */ getCaretPosition),\n/* harmony export */   getOperation: () => (/* binding */ getOperation),\n/* harmony export */   getSelection: () => (/* binding */ getSelection),\n/* harmony export */   isReadOnly: () => (/* binding */ isReadOnly),\n/* harmony export */   setCaretPosition: () => (/* binding */ setCaretPosition)\n/* harmony export */ });\nfunction isReadOnly(element) {\n  return element.hasAttribute('readonly');\n} // Gets <input/> selection bounds\n\nfunction getSelection(element) {\n  // If no selection, return nothing\n  if (element.selectionStart === element.selectionEnd) {\n    return;\n  }\n\n  return {\n    start: element.selectionStart,\n    end: element.selectionEnd\n  };\n} // Key codes\n\nvar Keys = {\n  Backspace: 8,\n  Delete: 46\n}; // Finds out the operation to be intercepted and performed\n// based on the key down event `keyCode`.\n\nfunction getOperation(event) {\n  switch (event.keyCode) {\n    case Keys.Backspace:\n      return 'Backspace';\n\n    case Keys.Delete:\n      return 'Delete';\n  }\n} // Gets <input/> caret position\n\nfunction getCaretPosition(element) {\n  return element.selectionStart;\n} // Sets <input/> caret position\n\nfunction setCaretPosition(element, caret_position) {\n  // Sanity check\n  if (caret_position === undefined) {\n    return;\n  } // Set caret position.\n  // There has been an issue with caret positioning on Android devices.\n  // https://github.com/catamphetamine/input-format/issues/2\n  // I was revisiting this issue and looked for similar issues in other libraries.\n  // For example, there's [`text-mask`](https://github.com/text-mask/text-mask) library.\n  // They've had exactly the same issue when the caret seemingly refused to be repositioned programmatically.\n  // The symptoms were the same: whenever the caret passed through a non-digit character of a mask (a whitespace, a bracket, a dash, etc), it looked as if it placed itself one character before its correct position.\n  // https://github.com/text-mask/text-mask/issues/300\n  // They seem to have found a basic fix for it: calling `input.setSelectionRange()` in a timeout rather than instantly for Android devices.\n  // https://github.com/text-mask/text-mask/pull/400/files\n  // I've implemented the same workaround here.\n\n\n  if (isAndroid()) {\n    setTimeout(function () {\n      return element.setSelectionRange(caret_position, caret_position);\n    }, 0);\n  } else {\n    element.setSelectionRange(caret_position, caret_position);\n  }\n}\n\nfunction isAndroid() {\n  // `navigator` is not defined when running mocha tests.\n  if (typeof navigator !== 'undefined') {\n    return ANDROID_USER_AGENT_REG_EXP.test(navigator.userAgent);\n  }\n}\n\nvar ANDROID_USER_AGENT_REG_EXP = /Android/i;\n//# sourceMappingURL=dom.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/dom.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/edit.js":
/*!***************************************************!*\
  !*** ./node_modules/input-format/modules/edit.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ edit)\n/* harmony export */ });\n// Edits text `value` (if `operation` is passed) and repositions the `caret` if needed.\n//\n// Example:\n//\n// value - '88005553535'\n// caret - 2 // starting from 0; is positioned before the first zero\n// operation - 'Backspace'\n//\n// Returns\n// {\n// \tvalue: '8005553535'\n// \tcaret: 1\n// }\n//\n// Currently supports just 'Delete' and 'Backspace' operations\n//\nfunction edit(value, caret, operation) {\n  switch (operation) {\n    case 'Backspace':\n      // If there exists the previous character,\n      // then erase it and reposition the caret.\n      if (caret > 0) {\n        // Remove the previous character\n        value = value.slice(0, caret - 1) + value.slice(caret); // Position the caret where the previous (erased) character was\n\n        caret--;\n      }\n\n      break;\n\n    case 'Delete':\n      // Remove current digit (if any)\n      value = value.slice(0, caret) + value.slice(caret + 1);\n      break;\n  }\n\n  return {\n    value: value,\n    caret: caret\n  };\n}\n//# sourceMappingURL=edit.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/edit.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/format.js":
/*!*****************************************************!*\
  !*** ./node_modules/input-format/modules/format.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ format)\n/* harmony export */ });\n/* harmony import */ var _templateFormatter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./templateFormatter.js */ \"(ssr)/./node_modules/input-format/modules/templateFormatter.js\");\n // Formats `value` value preserving `caret` at the same character.\n//\n// `{ value, caret }` attribute is the result of `parse()` function call.\n//\n// Returns `{ text, caret }` where the new `caret` is the caret position\n// inside `text` text corresponding to the original `caret` position inside `value`.\n//\n// `formatter(value)` is a function returning `{ text, template }`.\n//\n// `text` is the `value` value formatted using `template`.\n// It may either cut off the non-filled right part of the `template`\n// or it may fill the non-filled character placeholders\n// in the right part of the `template` with `spacer`\n// which is a space (' ') character by default.\n//\n// `template` is the template used to format the `value`.\n// It can be either a full-length template or a partial template.\n//\n// `formatter` can also be a string — a `template`\n// where character placeholders are denoted by 'x'es.\n// In this case `formatter` function is automatically created.\n//\n// Example:\n//\n// `value` is '880',\n// `caret` is `2` (before the first `0`)\n//\n// `formatter` is `'880' =>\n//   { text: '8 (80 )', template: 'x (xxx) xxx-xx-xx' }`\n//\n// The result is `{ text: '8 (80 )', caret: 4 }`.\n//\n\nfunction format(value, caret, formatter) {\n  if (typeof formatter === 'string') {\n    formatter = (0,_templateFormatter_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(formatter);\n  }\n\n  var _ref = formatter(value) || {},\n      text = _ref.text,\n      template = _ref.template;\n\n  if (text === undefined) {\n    text = value;\n  }\n\n  if (template) {\n    if (caret === undefined) {\n      caret = text.length;\n    } else {\n      var index = 0;\n      var found = false;\n      var possibly_last_input_character_index = -1;\n\n      while (index < text.length && index < template.length) {\n        // Character placeholder found\n        if (text[index] !== template[index]) {\n          if (caret === 0) {\n            found = true;\n            caret = index;\n            break;\n          }\n\n          possibly_last_input_character_index = index;\n          caret--;\n        }\n\n        index++;\n      } // If the caret was positioned after last input character,\n      // then the text caret index is just after the last input character.\n\n\n      if (!found) {\n        caret = possibly_last_input_character_index + 1;\n      }\n    }\n  }\n\n  return {\n    text: text,\n    caret: caret\n  };\n}\n//# sourceMappingURL=format.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/format.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/helpers.js":
/*!******************************************************!*\
  !*** ./node_modules/input-format/modules/helpers.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   count_occurences: () => (/* binding */ count_occurences)\n/* harmony export */ });\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\n// Counts all occurences of a symbol in a string\nfunction count_occurences(symbol, string) {\n  var count = 0; // Using `.split('')` here instead of normal `for ... of`\n  // because the importing application doesn't neccessarily include an ES6 polyfill.\n  // The `.split('')` approach discards \"exotic\" UTF-8 characters\n  // (the ones consisting of four bytes)\n  // but template placeholder characters don't fall into that range\n  // so skipping such miscellaneous \"exotic\" characters\n  // won't matter here for just counting placeholder character occurrences.\n\n  for (var _iterator = _createForOfIteratorHelperLoose(string.split('')), _step; !(_step = _iterator()).done;) {\n    var character = _step.value;\n\n    if (character === symbol) {\n      count++;\n    }\n  }\n\n  return count;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/inputControl.js":
/*!***********************************************************!*\
  !*** ./node_modules/input-format/modules/inputControl.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   onChange: () => (/* binding */ onChange),\n/* harmony export */   onCut: () => (/* binding */ onCut),\n/* harmony export */   onKeyDown: () => (/* binding */ onKeyDown),\n/* harmony export */   onPaste: () => (/* binding */ onPaste)\n/* harmony export */ });\n/* harmony import */ var _edit_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./edit.js */ \"(ssr)/./node_modules/input-format/modules/edit.js\");\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parse.js */ \"(ssr)/./node_modules/input-format/modules/parse.js\");\n/* harmony import */ var _format_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./format.js */ \"(ssr)/./node_modules/input-format/modules/format.js\");\n/* harmony import */ var _dom_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dom.js */ \"(ssr)/./node_modules/input-format/modules/dom.js\");\n\n\n\n // Deprecated.\n// I don't know why this function exists.\n\nfunction onCut(event, input, _parse, _format, on_change) {\n  if ((0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.isReadOnly)(input)) {\n    return;\n  } // The actual cut hasn't happened just yet hence the timeout.\n\n\n  setTimeout(function () {\n    return formatInputText(input, _parse, _format, undefined, on_change);\n  }, 0);\n} // Deprecated.\n// I don't know why this function exists.\n\nfunction onPaste(event, input, _parse, _format, on_change) {\n  if ((0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.isReadOnly)(input)) {\n    return;\n  }\n\n  var selection = (0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.getSelection)(input); // If selection is made,\n  // just erase the selected text\n  // prior to pasting\n\n  if (selection) {\n    eraseSelection(input, selection);\n  }\n\n  formatInputText(input, _parse, _format, undefined, on_change);\n}\nfunction onChange(event, input, _parse, _format, on_change) {\n  formatInputText(input, _parse, _format, undefined, on_change);\n} // \"Delete\" and \"Backspace\" keys are special\n// in a way that they're not handled by the regular `onChange()` handler\n// and instead are intercepted and re-applied manually.\n// The reason is that normally hitting \"Backspace\" or \"Delete\"\n// results in erasing a character, but that character might be any character,\n// while it would be a better \"user experience\" if it erased not just any character\n// but the closest \"meaningful\" character.\n// For example, if a template is `(xxx) xxx-xxxx`,\n// and the `<input/>` value is `(111) 222-3333`,\n// then, if a user begins erasing the `3333` part via \"Backspace\"\n// and reaches the \"-\" character, then it would just erase the \"-\" character.\n// Nothing wrong with that, but it would be a better \"user experience\"\n// if hitting \"Backspace\" at that position would erase the closest \"meaningful\"\n// character, which would be the rightmost `2`.\n// So, what this `onKeyDown()` handler does is it intercepts\n// \"Backspace\" and \"Delete\" keys and re-applies those operations manually\n// following the logic described above.\n\nfunction onKeyDown(event, input, _parse, _format, on_change) {\n  if ((0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.isReadOnly)(input)) {\n    return;\n  }\n\n  var operation = (0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.getOperation)(event);\n\n  switch (operation) {\n    case 'Delete':\n    case 'Backspace':\n      // Intercept this operation and perform it manually.\n      event.preventDefault();\n      var selection = (0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.getSelection)(input); // If a selection is made, just erase the selected text.\n\n      if (selection) {\n        eraseSelection(input, selection);\n        return formatInputText(input, _parse, _format, undefined, on_change);\n      } // Else, perform the (character erasing) operation manually.\n\n\n      return formatInputText(input, _parse, _format, operation, on_change);\n\n    default: // Will be handled normally as part of the `onChange` handler.\n\n  }\n}\n/**\r\n * Erases the selected text inside an `<input/>`.\r\n * @param  {DOMElement} input\r\n * @param  {Selection} selection\r\n */\n\nfunction eraseSelection(input, selection) {\n  var text = input.value;\n  text = text.slice(0, selection.start) + text.slice(selection.end);\n  input.value = text;\n  (0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.setCaretPosition)(input, selection.start);\n}\n/**\r\n * Parses and re-formats `<input/>` textual value.\r\n * E.g. when a user enters something into the `<input/>`\r\n * that raw input must first be parsed and the re-formatted properly.\r\n * Is called either after some user input (e.g. entered a character, pasted something)\r\n * or after the user performed an `operation` (e.g. \"Backspace\", \"Delete\").\r\n * @param  {DOMElement} input\r\n * @param  {Function} parse\r\n * @param  {Function} format\r\n * @param  {string} [operation] - The operation that triggered `<input/>` textual value change. E.g. \"Backspace\", \"Delete\".\r\n * @param  {Function} onChange\r\n */\n\n\nfunction formatInputText(input, _parse, _format, operation, on_change) {\n  // Parse `<input/>` textual value.\n  // Get the `value` and `caret` position.\n  var _parse2 = (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input.value, (0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.getCaretPosition)(input), _parse),\n      value = _parse2.value,\n      caret = _parse2.caret; // If a user performed an operation (\"Backspace\", \"Delete\")\n  // then apply that operation and get the new `value` and `caret` position.\n\n\n  if (operation) {\n    var newValueAndCaret = (0,_edit_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(value, caret, operation);\n    value = newValueAndCaret.value;\n    caret = newValueAndCaret.caret;\n  } // Format the `value`.\n  // (and reposition the caret accordingly)\n\n\n  var formatted = (0,_format_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(value, caret, _format);\n  var text = formatted.text;\n  caret = formatted.caret; // Set `<input/>` textual value manually\n  // to prevent React from resetting the caret position\n  // later inside a subsequent `render()`.\n  // Doesn't work for custom `inputComponent`s for some reason.\n\n  input.value = text; // Position the caret properly.\n\n  (0,_dom_js__WEBPACK_IMPORTED_MODULE_0__.setCaretPosition)(input, caret); // If the `<input/>` textual value did change,\n  // then the parsed `value` may have changed too.\n\n  if (on_change) {\n    on_change(value);\n  }\n}\n//# sourceMappingURL=inputControl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/inputControl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/parse.js":
/*!****************************************************!*\
  !*** ./node_modules/input-format/modules/parse.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ parse)\n/* harmony export */ });\n// Parses the `text`.\n//\n// Returns `{ value, caret }` where `caret` is\n// the caret position inside `value`\n// corresponding to the `caret_position` inside `text`.\n//\n// The `text` is parsed by feeding each character sequentially to\n// `parse_character(character, value, context)` function\n// and appending the result (if it's not `undefined`) to `value`.\n//\n// `context` argument is just a utility empty object that is shared within the bounds\n// of parsing a single input string. The `_parse()` function could use that object\n// to store any kind of \"flags\" in it in order to alter its behavior based when\n// parsing next characters within the same string. Or it could completely ignore it.\n//\n// Example:\n//\n// `text` is `8 (800) 555-35-35`,\n// `caret_position` is `4` (before the first `0`).\n// `parse_character` is `(character, value) =>\n//   if (character >= '0' && character <= '9') { return character }`.\n//\n// then `parse()` outputs `{ value: '88005553535', caret: 2 }`.\n//\nfunction parse(text, caret_position, parse_character) {\n  var context = {};\n  var value = '';\n  var focused_input_character_index = 0;\n  var index = 0;\n\n  while (index < text.length) {\n    var character = parse_character(text[index], value, context);\n\n    if (character !== undefined) {\n      value += character;\n\n      if (caret_position !== undefined) {\n        if (caret_position === index) {\n          focused_input_character_index = value.length - 1;\n        } else if (caret_position > index) {\n          focused_input_character_index = value.length;\n        }\n      }\n    }\n\n    index++;\n  } // If caret position wasn't specified\n\n\n  if (caret_position === undefined) {\n    // Then set caret position to \"after the last input character\"\n    focused_input_character_index = value.length;\n  }\n\n  var result = {\n    value: value,\n    caret: focused_input_character_index\n  };\n  return result;\n}\n//# sourceMappingURL=parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/react/Input.js":
/*!**********************************************************!*\
  !*** ./node_modules/input-format/modules/react/Input.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prop-types */ \"(ssr)/./node_modules/prop-types/index.js\");\n/* harmony import */ var _useInput_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useInput.js */ \"(ssr)/./node_modules/input-format/modules/react/useInput.js\");\nvar _excluded = [\"inputComponent\", \"parse\", \"format\", \"value\", \"defaultValue\", \"onChange\", \"controlled\", \"onKeyDown\", \"type\"];\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n// This is just `./ReactInput.js` rewritten in Hooks.\n\n\n // Usage:\n//\n// <ReactInput\n// \tvalue={this.state.phone}\n// \tonChange={phone => this.setState({ phone })}\n// \tparse={character => character}\n// \tformat={value => ({ text: value, template: 'xxxxxxxx' })}/>\n//\n\nfunction Input(_ref, ref) {\n  var _ref$inputComponent = _ref.inputComponent,\n      InputComponent = _ref$inputComponent === void 0 ? 'input' : _ref$inputComponent,\n      parse = _ref.parse,\n      format = _ref.format,\n      value = _ref.value,\n      defaultValue = _ref.defaultValue,\n      onChange = _ref.onChange,\n      controlled = _ref.controlled,\n      onKeyDown = _ref.onKeyDown,\n      _ref$type = _ref.type,\n      type = _ref$type === void 0 ? 'text' : _ref$type,\n      rest = _objectWithoutProperties(_ref, _excluded);\n\n  var inputProps = (0,_useInput_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_objectSpread({\n    ref: ref,\n    parse: parse,\n    format: format,\n    value: value,\n    defaultValue: defaultValue,\n    onChange: onChange,\n    controlled: controlled,\n    onKeyDown: onKeyDown,\n    type: type\n  }, rest));\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(InputComponent, inputProps);\n}\n\nInput = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(Input);\nInput.propTypes = {\n  // Parses a single characher of `<input/>` text.\n  parse: prop_types__WEBPACK_IMPORTED_MODULE_2__.func.isRequired,\n  // Formats `value` into `<input/>` text.\n  format: prop_types__WEBPACK_IMPORTED_MODULE_2__.func.isRequired,\n  // Renders `<input/>` by default.\n  inputComponent: prop_types__WEBPACK_IMPORTED_MODULE_2__.elementType,\n  // `<input/>` `type` attribute.\n  type: prop_types__WEBPACK_IMPORTED_MODULE_2__.string,\n  // Is parsed from <input/> text.\n  value: prop_types__WEBPACK_IMPORTED_MODULE_2__.string,\n  // An initial value for an \"uncontrolled\" <input/>.\n  defaultValue: prop_types__WEBPACK_IMPORTED_MODULE_2__.string,\n  // This handler is called each time `<input/>` text is changed.\n  onChange: prop_types__WEBPACK_IMPORTED_MODULE_2__.func,\n  // Whether this input should be \"controlled\" or \"uncontrolled\".\n  // The default value is `true` meaning \"uncontrolled\".\n  controlled: prop_types__WEBPACK_IMPORTED_MODULE_2__.bool,\n  // Passthrough\n  onKeyDown: prop_types__WEBPACK_IMPORTED_MODULE_2__.func,\n  onCut: prop_types__WEBPACK_IMPORTED_MODULE_2__.func,\n  onPaste: prop_types__WEBPACK_IMPORTED_MODULE_2__.func\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Input);\n//# sourceMappingURL=Input.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/react/Input.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/react/useInput.js":
/*!*************************************************************!*\
  !*** ./node_modules/input-format/modules/react/useInput.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useInput)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _inputControl_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../inputControl.js */ \"(ssr)/./node_modules/input-format/modules/inputControl.js\");\nvar _excluded = [\"ref\", \"parse\", \"format\", \"value\", \"defaultValue\", \"controlled\", \"onChange\", \"onKeyDown\"];\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\n\n\nfunction useInput(_ref) {\n  var ref = _ref.ref,\n      parse = _ref.parse,\n      format = _ref.format,\n      value = _ref.value,\n      defaultValue = _ref.defaultValue,\n      _ref$controlled = _ref.controlled,\n      controlled = _ref$controlled === void 0 ? true : _ref$controlled,\n      onChange = _ref.onChange,\n      onKeyDown = _ref.onKeyDown,\n      rest = _objectWithoutProperties(_ref, _excluded);\n\n  // It doesn't seem to be required to alert a developer about controlled/uncontrolled misuse:\n  // if `controlled` is `true` then `defaultValue` is simply ignored.\n  //\n  // if (defaultValue && controlled) {\n  // \tconsole.error('[input-format] You\\'ve passed both `defaultValue` and `controlled: true` properties which is an invalid use case')\n  // }\n  var internalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  var setRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (instance) {\n    internalRef.current = instance;\n\n    if (ref) {\n      if (typeof ref === 'function') {\n        ref(instance);\n      } else {\n        ref.current = instance;\n      }\n    }\n  }, [ref]);\n\n  var _onChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    return (0,_inputControl_js__WEBPACK_IMPORTED_MODULE_1__.onChange)(event, internalRef.current, parse, format, onChange);\n  }, [internalRef, parse, format, onChange]);\n\n  var _onKeyDown = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {\n    if (onKeyDown) {\n      onKeyDown(event);\n    } // If `onKeyDown()` handler above has called `event.preventDefault()`\n    // then ignore this `keydown` event.\n\n\n    if (event.defaultPrevented) {\n      return;\n    }\n\n    return (0,_inputControl_js__WEBPACK_IMPORTED_MODULE_1__.onKeyDown)(event, internalRef.current, parse, format, onChange);\n  }, [internalRef, parse, format, onChange, onKeyDown]);\n\n  var commonProps = _objectSpread(_objectSpread({}, rest), {}, {\n    ref: setRef,\n    onChange: _onChange,\n    onKeyDown: _onKeyDown\n  });\n\n  if (controlled) {\n    return _objectSpread(_objectSpread({}, commonProps), {}, {\n      value: format(isEmptyValue(value) ? '' : value).text\n    });\n  }\n\n  return _objectSpread(_objectSpread({}, commonProps), {}, {\n    defaultValue: format(isEmptyValue(defaultValue) ? '' : defaultValue).text\n  });\n}\n\nfunction isEmptyValue(value) {\n  return value === undefined || value === null;\n}\n//# sourceMappingURL=useInput.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/react/useInput.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/input-format/modules/templateFormatter.js":
/*!****************************************************************!*\
  !*** ./node_modules/input-format/modules/templateFormatter.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers.js */ \"(ssr)/./node_modules/input-format/modules/helpers.js\");\n/* harmony import */ var _closeBraces_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./closeBraces.js */ \"(ssr)/./node_modules/input-format/modules/closeBraces.js\");\nfunction _createForOfIteratorHelperLoose(o, allowArrayLike) { var it = typeof Symbol !== \"undefined\" && o[Symbol.iterator] || o[\"@@iterator\"]; if (it) return (it = it.call(o)).next.bind(it); if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === \"number\") { if (it) o = it; var i = 0; return function () { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\n\n // Takes a `template` where character placeholders\n// are denoted by 'x'es (e.g. 'x (xxx) xxx-xx-xx').\n//\n// Returns a function which takes `value` characters\n// and returns the `template` filled with those characters.\n// If the `template` can only be partially filled\n// then it is cut off.\n//\n// If `shouldCloseBraces` is `true`,\n// then it will also make sure all dangling braces are closed,\n// e.g. \"8 (8\" -> \"8 (8  )\" (iPhone style phone number input).\n//\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(template) {\n  var placeholder = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'x';\n  var shouldCloseBraces = arguments.length > 2 ? arguments[2] : undefined;\n\n  if (!template) {\n    return function (value) {\n      return {\n        text: value\n      };\n    };\n  }\n\n  var placeholdersCountInTemplate = (0,_helpers_js__WEBPACK_IMPORTED_MODULE_0__.count_occurences)(placeholder, template);\n  return function (value) {\n    if (!value) {\n      return {\n        text: '',\n        template: template\n      };\n    }\n\n    var characterIndexInValue = 0;\n    var templateWithFilledInPlaceholders = ''; // Using `.split('')` here instead of normal `for ... of`\n    // because the importing application doesn't neccessarily include an ES6 polyfill.\n    // The `.split('')` approach discards \"exotic\" UTF-8 characters\n    // (the ones consisting of four bytes)\n    // but template placeholder characters don't fall into that range\n    // and appending UTF-8 characters to a string in parts still works.\n\n    for (var _iterator = _createForOfIteratorHelperLoose(template.split('')), _step; !(_step = _iterator()).done;) {\n      var character = _step.value;\n\n      if (character !== placeholder) {\n        templateWithFilledInPlaceholders += character;\n        continue;\n      }\n\n      templateWithFilledInPlaceholders += value[characterIndexInValue];\n      characterIndexInValue++; // If the last available value character has been filled in,\n      // then return the filled in template\n      // (either trim the right part or retain it,\n      //  if no more character placeholders in there)\n\n      if (characterIndexInValue === value.length) {\n        // If there are more character placeholders\n        // in the right part of the template\n        // then simply trim it.\n        if (value.length < placeholdersCountInTemplate) {\n          break;\n        }\n      }\n    }\n\n    if (shouldCloseBraces) {\n      templateWithFilledInPlaceholders = (0,_closeBraces_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(templateWithFilledInPlaceholders, template);\n    }\n\n    return {\n      text: templateWithFilledInPlaceholders,\n      template: template\n    };\n  };\n}\n//# sourceMappingURL=templateFormatter.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/input-format/modules/templateFormatter.js\n");

/***/ })

};
;